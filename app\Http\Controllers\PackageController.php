<?php

namespace App\Http\Controllers;

use App\Models\Package;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class PackageController extends BaseResourceController
{
    protected function getModelClass()
    {
        return Package::class;
    }

    protected function getViewPrefix()
    {
        return 'packages';
    }

    protected function getRoutePrefix()
    {
        return 'packages';
    }

    protected function getImageDirectory()
    {
        return 'packages';
    }

    protected function getValidationRules(Request $request, $model = null)
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ];
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Package  $package
     * @return \Illuminate\Http\Response
     */
    public function show(Package $package)
    {
        return view('packages.show', compact('package'));
    }
}
