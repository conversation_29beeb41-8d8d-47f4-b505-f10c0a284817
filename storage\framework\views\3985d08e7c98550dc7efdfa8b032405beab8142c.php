<?php $__env->startSection('title', 'จัดการหมวดหมู่กิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">จัดการหมวดหมู่กิจกรรม</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.activities.index')); ?>">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item active">จัดการหมวดหมู่</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการหมวดหมู่กิจกรรม</h3>
                            <div class="card-tools">
                                <a href="<?php echo e(route('admin.activity-categories.create')); ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <?php echo e(session('success')); ?>

                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            <?php endif; ?>

                            <?php if(session('error')): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <?php echo e(session('error')); ?>

                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            <?php endif; ?>

                            <?php if($categories->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 50px;">สี</th>
                                                <th>ชื่อหมวดหมู่</th>
                                                <th>คำอธิบาย</th>
                                                <th style="width: 100px;">จำนวนกิจกรรม</th>
                                                <th style="width: 80px;">สถานะ</th>
                                                <th style="width: 120px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td class="text-center">
                                                        <div class="color-box" 
                                                             style="width: 30px; height: 30px; background-color: <?php echo e($category->color); ?>; border-radius: 4px; margin: 0 auto;"></div>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo e($category->name); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php echo e($category->description ?? '-'); ?>

                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge badge-info">
                                                            <?php echo e($category->activities_count); ?>

                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php if($category->is_active): ?>
                                                            <span class="badge badge-success">ใช้งาน</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-secondary">ปิดใช้งาน</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<?php echo e(route('admin.activity-categories.edit', $category)); ?>" 
                                                               class="btn btn-warning btn-sm" title="แก้ไข">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <?php if($category->activities_count == 0): ?>
                                                                <form action="<?php echo e(route('admin.activity-categories.destroy', $category)); ?>" 
                                                                      method="POST" class="d-inline"
                                                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้?')">
                                                                    <?php echo csrf_field(); ?>
                                                                    <?php echo method_field('DELETE'); ?>
                                                                    <button type="submit" class="btn btn-danger btn-sm" title="ลบ">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            <?php else: ?>
                                                                <button class="btn btn-danger btn-sm" disabled title="ไม่สามารถลบได้เนื่องจากมีกิจกรรมในหมวดหมู่นี้">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีหมวดหมู่</h5>
                                    <p class="text-muted">เริ่มต้นสร้างหมวดหมู่แรกของคุณ</p>
                                    <a href="<?php echo e(route('admin.activity-categories.create')); ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>เพิ่มหมวดหมู่ใหม่
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activity-categories/index.blade.php ENDPATH**/ ?>