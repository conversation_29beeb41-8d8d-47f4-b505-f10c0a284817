# วิธีการเปิดใช้งาน GD Extension ใน XAMPP

## ปัญหาที่พบ
- รูปภาพไม่ถูก resize เมื่ออัปโหลด
- ระบบจะเก็บรูปภาพต้นฉบับโดยไม่ปรับขนาด
- ข้อความ warning ใน log: "Neither GD nor Imagick extension available"

## วิธีแก้ไข

### 1. เปิด XAMPP Control Panel
- เปิดโปรแกรม XAMPP Control Panel
- หยุด Apache server ก่อน (กดปุ่ม Stop)

### 2. แก้ไขไฟล์ php.ini
- คลิกปุ่ม "Config" ข้างๆ Apache
- เลือก "PHP (php.ini)"
- ไฟล์ php.ini จะเปิดขึ้นมา

### 3. ค้นหาและแก้ไข GD Extension
- กด Ctrl+F เพื่อค้นหา
- ค้นหา: `;extension=gd`
- ลบเครื่องหมาย `;` ออกจากหน้าบรรทัด
- เปลี่ยนจาก: `;extension=gd`
- เป็น: `extension=gd`

### 4. บันทึกและรีสตาร์ท
- บันทึกไฟล์ php.ini (Ctrl+S)
- ปิดไฟล์
- เริ่ม Apache server ใหม่ (กดปุ่ม Start)

### 5. ตรวจสอบการติดตั้ง
เปิด Terminal/Command Prompt และรันคำสั่ง:
```bash
php -m | findstr -i gd
```

หรือสร้างไฟล์ PHP เพื่อตรวจสอบ:
```php
<?php
if (extension_loaded('gd')) {
    echo "✅ GD Extension is loaded!";
    print_r(gd_info());
} else {
    echo "❌ GD Extension is NOT loaded";
}
?>
```

## ผลลัพธ์หลังจากเปิดใช้งาน GD
- รูปภาพจะถูก resize อัตโนมัติเป็น 800x600 pixels
- ขนาดไฟล์จะเล็กลง
- ประสิทธิภาพการโหลดเว็บไซต์ดีขึ้น
- ไม่มี warning message ใน log

## หมายเหตุ
- หาก GD extension ไม่สามารถเปิดใช้งานได้ ระบบจะยังคงทำงานได้ปกติ
- รูปภาพจะถูกเก็บในขนาดต้นฉบับ
- ควรตรวจสอบขนาดไฟล์ก่อนอัปโหลด (ไม่เกิน 2MB)
